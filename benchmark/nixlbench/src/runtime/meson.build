# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

runtime_sources = [
  'runtime.cpp',
  'runtime.h',
]

# Create a runtime-specific include directory
runtime_inc = include_directories('.')

# Use the utils_lib directly instead of trying to get it as a dependency
runtime_lib = static_library('runtime',
  runtime_sources,
  include_directories: [inc_dir, runtime_inc],
  install: true,
)

nixlbench_runtimes = [runtime_lib]
rt_deps = []
rt_inc_deps = []
# Include subdirectories
if etcd_available
    subdir('etcd')
    nixlbench_runtimes += etcd_rt_lib
    rt_deps = etcd_dep
    rt_inc_deps += etcd_inc
endif

runtime_interface = declare_dependency(
  link_with: nixlbench_runtimes,
  dependencies: rt_deps,
  include_directories: [etcd_inc]
)
