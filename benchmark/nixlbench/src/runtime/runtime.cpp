/*
 * SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "runtime/runtime.h"

int xferBenchRT::getSize() const {
    return size;
}

int xferBenchRT::getRank() const {
    return rank;
}


int xferBenchRT::sendInt(int *buffer, int dest_rank) {
    return 0;
}

int xferBenchRT::recvInt(int *buffer, int src_rank) {
    return 0;
}

int xferBenchRT::broadcastInt(int *buffer, size_t count, int root_rank) {
    return 0;
}

int xferBenchRT::sendChar(char *buffer, size_t count, int dest_rank) {
    return 0;
}

int xferBenchRT::recvChar(char *buffer, size_t count, int src_rank) {
    return 0;
}

int xferBenchRT::reduceSumDouble(double *local_buffer, double *global_buffer, int dest_rank) {
    return 0;
}

int xferBenchRT::barrier(const std::string& barrier_id) {
    return 0;
}
