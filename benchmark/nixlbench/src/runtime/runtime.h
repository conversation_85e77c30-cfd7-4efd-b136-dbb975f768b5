/*
 * SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef __RUNTIME_H
#define __RUNTIME_H

#include <iostream>
#include <string>

class xferBenchRTReq {
    public:
        friend class xferBenchRT;
};

class xferBenchRT {
    private:
        int size;
        int rank;
    protected:
        void setSize(int s) { size = s; };
        void setRank(int r) { rank = r; };
    public:
    	virtual ~xferBenchRT() {};

        int getSize() const;
        int getRank() const;
        virtual int sendInt(int *buffer, int dest_rank) = 0;
        virtual int recvInt(int *buffer, int src_rank) = 0;
        virtual int broadcastInt(int *buffer, size_t count, int root_rank) = 0;
        virtual int sendChar(char *buffer, size_t count, int dest_rank) = 0;
        virtual int recvChar(char *buffer, size_t count, int src_rank) = 0;
        virtual int reduceSumDouble(double *local_value, double *global_value, int dest_rank) = 0;

        // Add a barrier function to synchronize all processes
        virtual int barrier(const std::string& barrier_id) = 0;
};

#endif // __RUNTIME_H
