/*
 * SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef __SCOPE_GUARD_H
#define __SCOPE_GUARD_H

#include <utility>

template <typename F>
class ScopeGuard {
public:
    explicit ScopeGuard(F func) : func_(std::move(func)) {}

    ScopeGuard(const ScopeGuard&) = delete;
    ScopeGuard(ScopeGuard&& other) = delete;
    ScopeGuard& operator=(const ScopeGuard&) = delete;

    ~ScopeGuard() {
        func_();
    }

private:
    F func_;
};

// Factory function to simplify creation
template <typename F>
ScopeGuard<F> make_scope_guard(F func) {
    return ScopeGuard<F>(std::move(func));
}

#endif // __SCOPE_GUARD_H
