# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

strategy:
  tp_size: 8
  pp_size: 32
  model_quant_mode: "fp8"
  kvcache_quant_mode: "fp8"

runtime:
  isl: 1000
  osl: 100
  num_requests: 10

system:
  hardware: "H100"
  backend: "SGLANG"
  access_pattern: "layer"
  page_size: 16