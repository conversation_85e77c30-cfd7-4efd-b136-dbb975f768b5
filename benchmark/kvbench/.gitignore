# Insta snapshots.
*.pending-snap

# Generated by Cargo
# will have compiled files and executables
debug/
target/
target-alpine/

# Bootstrapped Python versions
/bin/

# These are backup files generated by rustfmt
**/*.rs.bk

# MSVC Windows builds of rustc generate these, which store debugging information
*.pdb

# Python tmp files
__pycache__
test/__pycache__
# Maturin builds, and other native editable builds
*.so
*.pyd
*.dll
/dist
/crates/uv-build/dist

# Profiling
flamegraph.svg
perf.data
perf.data.old
profile.json

# MkDocs
/site

# macOS
**/.DS_Store

# IDE
.idea
.vscode