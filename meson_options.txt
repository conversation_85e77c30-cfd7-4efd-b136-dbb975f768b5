# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

option('ucx_path', type: 'string', value: '', description: 'Path to UCX install')
option('etcd_inc_path', type: 'string', value: '', description: 'Path to ETCD Headers')
option('etcd_lib_path', type: 'string', value: '', description: 'Path to ETCD Libraries')
option('disable_gds_backend', type : 'boolean', value : false, description : 'disable gds backend')
option('disable_mooncake_backend', type : 'boolean', value : false, description : 'disable mooncake backend')
option('install_headers', type : 'boolean', value : true, description : 'install headers')
option('gds_path', type: 'string', value: '/usr/local/cuda/', description: 'Path to GDS CuFile install')
option('cudapath_inc', type: 'string', value: '', description: 'Include path for CUDA')
option('cudapath_lib', type: 'string', value: '', description: 'Library path for CUDA')
option('cudapath_stub', type: 'string', value: '', description: 'Extra Stub path for CUDA')
option('static_plugins', type: 'string', value: '', description: 'Plugins to be built-in, comma-separated')
option('build_docs', type: 'boolean', value: false, description: 'Build Doxygen documentation')
option('log_level', type: 'combo', choices: ['trace', 'debug', 'info', 'warning', 'error', 'fatal', 'auto'], value: 'auto', description: 'Log Level (auto: auto-detect based on build type: trace for debug builds, info for release builds)')

# Tests
option('test_all_plugins', type: 'boolean', value: false, description: 'Testing all plugins in addition to the mocks..')
