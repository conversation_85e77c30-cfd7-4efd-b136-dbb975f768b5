# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

[package]
name = "nixl-sys"
version = "0.3.1"
edition = "2021"
description = "Low-level bindings to the nixl library"
license = "Apache-2.0"
homepage = "https://github.com/ai-dynamo/nixl"
repository = "https://github.com/ai-dynamo/nixl.git"
authors = ["NIXL Developers <<EMAIL>>"]
readme = "README.md"
links = "nixl"
build = "build.rs"

[dependencies]
thiserror = { version = "2" }
tracing = { version = "0.1" }
serde = { version = "1", features = ["derive"] }

libc = "0.2"

[build-dependencies]
bindgen = "0.71"
cc = { version = "1.2.23", features = ["parallel"] }
pkg-config = "0.3"
os_info = "3.11"
