<!--
SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
SPDX-License-Identifier: Apache-2.0

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->
# utils tests

There are a handful of unit tests in this directory, described here:

- src/utils/serdes_test.cpp - test for just serialization/deserialization functions
- src/utils/ucx/ucx_worker_test.cpp - test for all UCX utility functions
- src/utils/ucx/ucx_am_test.cpp - test for UCX active message utilities
