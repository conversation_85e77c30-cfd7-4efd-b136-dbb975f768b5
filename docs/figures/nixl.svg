<svg width="13200" height="7425" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" overflow="hidden"><defs><filter id="fx0" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0.000000 0.000000"/><feFuncG type="discrete" tableValues="0.000000 0.000000"/><feFuncB type="discrete" tableValues="0.000000 0.000000"/><feFuncA type="linear" slope="0.380392" intercept="0.000000"/></feComponentTransfer><feGaussianBlur stdDeviation="4.811899 4.811899"/></filter><filter id="fx1" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0.000000 0.000000"/><feFuncG type="discrete" tableValues="0.000000 0.000000"/><feFuncB type="discrete" tableValues="0.000000 0.000000"/><feFuncA type="linear" slope="0.380392" intercept="0.000000"/></feComponentTransfer><feGaussianBlur stdDeviation="4.811899 4.811899"/></filter><filter id="fx2" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0.000000 0.000000"/><feFuncG type="discrete" tableValues="0.000000 0.000000"/><feFuncB type="discrete" tableValues="0.000000 0.000000"/><feFuncA type="linear" slope="0.380392" intercept="0.000000"/></feComponentTransfer><feGaussianBlur stdDeviation="4.811899 4.811899"/></filter><filter id="fx3" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0.000000 0.000000"/><feFuncG type="discrete" tableValues="0.000000 0.000000"/><feFuncB type="discrete" tableValues="0.000000 0.000000"/><feFuncA type="linear" slope="0.380392" intercept="0.000000"/></feComponentTransfer><feGaussianBlur stdDeviation="4.811899 4.811899"/></filter><filter id="fx4" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0.000000 0.000000"/><feFuncG type="discrete" tableValues="0.000000 0.000000"/><feFuncB type="discrete" tableValues="0.000000 0.000000"/><feFuncA type="linear" slope="0.380392" intercept="0.000000"/></feComponentTransfer><feGaussianBlur stdDeviation="4.811899 4.811899"/></filter><filter id="fx5" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0.000000 0.000000"/><feFuncG type="discrete" tableValues="0.000000 0.000000"/><feFuncB type="discrete" tableValues="0.000000 0.000000"/><feFuncA type="linear" slope="0.380392" intercept="0.000000"/></feComponentTransfer><feGaussianBlur stdDeviation="4.811899 4.811899"/></filter><filter id="fx6" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0.000000 0.000000"/><feFuncG type="discrete" tableValues="0.000000 0.000000"/><feFuncB type="discrete" tableValues="0.000000 0.000000"/><feFuncA type="linear" slope="0.380392" intercept="0.000000"/></feComponentTransfer><feGaussianBlur stdDeviation="4.811899 4.811899"/></filter><filter id="fx7" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0.000000 0.000000"/><feFuncG type="discrete" tableValues="0.000000 0.000000"/><feFuncB type="discrete" tableValues="0.000000 0.000000"/><feFuncA type="linear" slope="0.380392" intercept="0.000000"/></feComponentTransfer><feGaussianBlur stdDeviation="4.811899 4.811899"/></filter><filter id="fx8" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0.000000 0.000000"/><feFuncG type="discrete" tableValues="0.000000 0.000000"/><feFuncB type="discrete" tableValues="0.000000 0.000000"/><feFuncA type="linear" slope="0.380392" intercept="0.000000"/></feComponentTransfer><feGaussianBlur stdDeviation="4.811899 4.811899"/></filter><filter id="fx9" x="-10%" y="-10%" width="120%" height="120%" filterUnits="userSpaceOnUse" primitiveUnits="userSpaceOnUse"><feComponentTransfer color-interpolation-filters="sRGB"><feFuncR type="discrete" tableValues="0.000000 0.000000"/><feFuncG type="discrete" tableValues="0.000000 0.000000"/><feFuncB type="discrete" tableValues="0.000000 0.000000"/><feFuncA type="linear" slope="0.380392" intercept="0.000000"/></feComponentTransfer><feGaussianBlur stdDeviation="4.811899 4.811899"/></filter><clipPath id="clip10"><rect x="1.60938" y="1.9021" width="2514.48" height="410.363"/></clipPath><clipPath id="clip11"><rect x="1.71289" y="146.839" width="2518.38" height="1035.62"/></clipPath><clipPath id="clip12"><rect x="117.565" y="1.7821" width="258.872" height="537.552"/></clipPath><clipPath id="clip13"><rect x="1.47021" y="162.782" width="886.23" height="1015.26"/></clipPath><clipPath id="clip14"><rect x="1.12158" y="1.73633" width="879.579" height="370.481"/></clipPath><clipPath id="clip15"><rect x="162.564" y="162.782" width="1047" height="672.087"/></clipPath><clipPath id="clip16"><rect x="162.564" y="1.7854" width="1049.12" height="931.866"/></clipPath><clipPath id="clip17"><rect x="117.565" y="231.782" width="258.872" height="537.552"/></clipPath><clipPath id="clip18"><rect x="117.564" y="1.78174" width="258.872" height="438.357"/></clipPath><clipPath id="clip19"><rect x="117.564" y="231.782" width="258.872" height="427.049"/></clipPath></defs><g><g><rect x="0" y="0" width="13200" height="7425" fill="#FFFFFF" fill-opacity="1"/><path d="M8900 2802.18C8900 2498.32 9146.32 2252 9450.18 2252L11650.8 2252C11954.7 2252 12201 2498.32 12201 2802.18L12201 5758.82C12201 6062.68 11954.7 6309 11650.8 6309L9450.18 6309C9146.32 6309 8900 6062.68 8900 5758.82Z" stroke="#003526" stroke-width="13.75" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-dasharray="110 41.25" stroke-opacity="1" fill="none" fill-rule="evenodd"/><text fill="#000000" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 9638.26 5766)">Node memories </text><text fill="#000000" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 9293.55 6063)">registered with Agent</text><path d="M746 2681.01C746 2444.07 938.073 2252 1175.01 2252L2890.99 2252C3127.93 2252 3320 2444.07 3320 2681.01L3320 5879.99C3320 6116.93 3127.93 6309 2890.99 6309L1175.01 6309C938.073 6309 746 6116.93 746 5879.99Z" stroke="#003526" stroke-width="13.75" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-dasharray="110 41.25" stroke-opacity="1" fill="none" fill-rule="evenodd"/><text fill="#000000" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 949.166 5799)">Remote Agent info </text><text fill="#000000" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1228.61 6096)">cached locally</text><path d="M3760.5 2996.18C3760.5 2808.58 3912.58 2656.5 4100.17 2656.5L8012.83 2656.5C8200.42 2656.5 8352.5 2808.58 8352.5 2996.18L8352.5 4354.83C8352.5 4542.42 8200.42 4694.5 8012.83 4694.5L4100.17 4694.5C3912.58 4694.5 3760.5 4542.42 3760.5 4354.83Z" stroke="#000000" stroke-width="20.625" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#C8C8C8" fill-rule="evenodd" fill-opacity="1"/><text fill="#000000" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 5166.38 3130)">Transfer Agent</text><g clip-path="url(#clip10)" filter="url(#fx0)" transform="translate(8177 3771)"><g><g><path d="M19.955 16.3377 2360.69 304.503 2356.78 336.263 16.045 48.0979ZM2352.63 239.028 2501.66 337.978 2333.08 397.829Z" fill="#016EC0" fill-rule="nonzero" fill-opacity="1"/></g></g></g><path d="M8196.96 3780.08 10537.3 4068.2 10533.4 4100.04 8193.04 3811.92ZM10529.2 4002.55 10678.7 4101.76 10509.6 4161.77Z" fill="#016EC0" fill-rule="nonzero" fill-opacity="1"/><g clip-path="url(#clip11)" filter="url(#fx1)" transform="translate(8173 2650)"><g><g><path d="M5.85145-14.8916 2355.49 908.363 2343.78 938.147-5.85145 14.8916ZM2364 842.945 2483.66 975.917 2305.48 991.861Z" fill="#016EC0" fill-rule="nonzero" fill-opacity="1" transform="matrix(1 0 0 -1 22 1153.14)"/></g></g></g><path d="M5.86669-14.9304 2355.15 908.187 2343.42 938.048-5.86669 14.9304ZM2363.69 842.598 2483.66 975.917 2305.02 991.902Z" fill="#016EC0" fill-rule="nonzero" fill-opacity="1" transform="matrix(1 0 0 -1 8195 3795.92)"/><path d="M3760 5476.17C3760 5354.57 3858.57 5256 3980.17 5256L8131.83 5256C8253.43 5256 8352 5354.57 8352 5476.17L8352 6356.83C8352 6478.43 8253.43 6577 8131.83 6577L3980.17 6577C3858.57 6577 3760 6478.43 3760 6356.83Z" stroke="#003526" stroke-width="13.75" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-dasharray="110 41.25" stroke-opacity="1" fill="none" fill-rule="evenodd"/><text fill="#000000" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 4071.78 6444)">Example Transfer Backend Plugins</text><path d="M3906 3802.5C3906 3458.15 4304.91 3179 4797 3179 5289.09 3179 5688 3458.15 5688 3802.5 5688 4146.85 5289.09 4426 4797 4426 4304.91 4426 3906 4146.85 3906 3802.5Z" fill="#73B401" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 4235.88 3737)">Metadata </text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 4342.61 4034)">Handler</text><path d="M6413 3796C6413 3451.93 6811.91 3173 7304 3173 7796.09 3173 8195 3451.93 8195 3796 8195 4140.07 7796.09 4419 7304 4419 6811.91 4419 6413 4140.07 6413 3796Z" fill="#73B401" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 6824.39 3730)">Memory </text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 6865.57 4027)">Section</text><g clip-path="url(#clip12)" filter="url(#fx2)" transform="translate(4521 1961)"><g><g><path d="M270 16.2178 270.001 386.899 224.001 386.899 224 16.2179ZM247.001 386.899 362.001 294.898 247.001 524.899 132.001 294.899Z" fill="#860D56" fill-rule="nonzero" fill-opacity="1"/></g></g></g><path d="M4790.92 1970 4790.92 2341.18 4745.08 2341.18 4745.08 1970ZM4768 2341.18 4882.58 2249.51 4768 2478.68 4653.42 2249.51Z" fill="#860D56" fill-rule="nonzero" fill-opacity="1"/><text fill="#000000" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 3776.08 1284)">NIXL cross</text><text fill="#000000" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 5019.61 1284)">-</text><text fill="#000000" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 5119.04 1284)">node </text><text fill="#000000" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 3992.07 1581)">&amp; cross</text><text fill="#000000" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 4858.05 1581)">-</text><text fill="#000000" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 4957.48 1581)">mem</text><text fill="#000000" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 3655.38 1878)">buffer list primitive</text><path d="M9041.5 2615.83C9041.5 2559.32 9087.32 2513.5 9143.83 2513.5L9939.17 2513.5C9995.68 2513.5 10041.5 2559.32 10041.5 2615.83L10041.5 3025.17C10041.5 3081.68 9995.68 3127.5 9939.17 3127.5L9143.83 3127.5C9087.32 3127.5 9041.5 3081.68 9041.5 3025.17Z" stroke="#000000" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="Aptos,Aptos_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 9210.57 2900)">DRAM</text><path d="M9136.5 2698.67C9136.5 2642.24 9182.24 2596.5 9238.67 2596.5L10034.3 2596.5C10090.8 2596.5 10136.5 2642.24 10136.5 2698.67L10136.5 3107.33C10136.5 3163.76 10090.8 3209.5 10034.3 3209.5L9238.67 3209.5C9182.24 3209.5 9136.5 3163.76 9136.5 3107.33Z" stroke="#000000" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="Aptos,Aptos_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 9305.92 2982)">DRAM</text><path d="M9231.5 2780.67C9231.5 2724.24 9277.24 2678.5 9333.67 2678.5L10129.3 2678.5C10185.8 2678.5 10231.5 2724.24 10231.5 2780.67L10231.5 3189.33C10231.5 3245.76 10185.8 3291.5 10129.3 3291.5L9333.67 3291.5C9277.24 3291.5 9231.5 3245.76 9231.5 3189.33Z" stroke="#000000" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 9399.94 3057)">DRAM</text><path d="M10679.5 2615.83C10679.5 2559.32 10725.3 2513.5 10781.8 2513.5L11577.2 2513.5C11633.7 2513.5 11679.5 2559.32 11679.5 2615.83L11679.5 3025.17C11679.5 3081.68 11633.7 3127.5 11577.2 3127.5L10781.8 3127.5C10725.3 3127.5 10679.5 3081.68 10679.5 3025.17Z" stroke="#000000" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="Aptos,Aptos_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 10849 2900)">DRAM</text><path d="M10774.5 2698.67C10774.5 2642.24 10820.2 2596.5 10876.7 2596.5L11672.3 2596.5C11728.8 2596.5 11774.5 2642.24 11774.5 2698.67L11774.5 3107.33C11774.5 3163.76 11728.8 3209.5 11672.3 3209.5L10876.7 3209.5C10820.2 3209.5 10774.5 3163.76 10774.5 3107.33Z" stroke="#000000" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="Aptos,Aptos_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 10944.3 2982)">DRAM</text><path d="M10870.5 2780.67C10870.5 2724.24 10916.2 2678.5 10972.7 2678.5L11768.3 2678.5C11824.8 2678.5 11870.5 2724.24 11870.5 2780.67L11870.5 3189.33C11870.5 3245.76 11824.8 3291.5 11768.3 3291.5L10972.7 3291.5C10916.2 3291.5 10870.5 3245.76 10870.5 3189.33Z" stroke="#000000" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 11045.2 3057)">VRAM</text><path d="M9041.5 3897.67C9041.5 3841.24 9087.24 3795.5 9143.67 3795.5L9939.33 3795.5C9995.76 3795.5 10041.5 3841.24 10041.5 3897.67L10041.5 4306.33C10041.5 4362.76 9995.76 4408.5 9939.33 4408.5L9143.67 4408.5C9087.24 4408.5 9041.5 4362.76 9041.5 4306.33Z" stroke="#000000" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="Aptos,Aptos_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 9210.57 4181)">DRAM</text><path d="M9136.5 3979.67C9136.5 3923.24 9182.24 3877.5 9238.67 3877.5L10034.3 3877.5C10090.8 3877.5 10136.5 3923.24 10136.5 3979.67L10136.5 4388.33C10136.5 4444.76 10090.8 4490.5 10034.3 4490.5L9238.67 4490.5C9182.24 4490.5 9136.5 4444.76 9136.5 4388.33Z" stroke="#000000" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="Aptos,Aptos_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 9305.92 4263)">DRAM</text><path d="M9231.5 4062.67C9231.5 4006.24 9277.24 3960.5 9333.67 3960.5L10129.3 3960.5C10185.8 3960.5 10231.5 4006.24 10231.5 4062.67L10231.5 4471.33C10231.5 4527.76 10185.8 4573.5 10129.3 4573.5L9333.67 4573.5C9277.24 4573.5 9231.5 4527.76 9231.5 4471.33Z" stroke="#000000" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 9504.21 4339)">FILE</text><path d="M10679.5 3897.67C10679.5 3841.24 10725.2 3795.5 10781.7 3795.5L11577.3 3795.5C11633.8 3795.5 11679.5 3841.24 11679.5 3897.67L11679.5 4306.33C11679.5 4362.76 11633.8 4408.5 11577.3 4408.5L10781.7 4408.5C10725.2 4408.5 10679.5 4362.76 10679.5 4306.33Z" stroke="#000000" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="Aptos,Aptos_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 10849 4181)">DRAM</text><path d="M10774.5 3979.67C10774.5 3923.24 10820.2 3877.5 10876.7 3877.5L11672.3 3877.5C11728.8 3877.5 11774.5 3923.24 11774.5 3979.67L11774.5 4388.33C11774.5 4444.76 11728.8 4490.5 11672.3 4490.5L10876.7 4490.5C10820.2 4490.5 10774.5 4444.76 10774.5 4388.33Z" stroke="#000000" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="Aptos,Aptos_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 10944.3 4263)">DRAM</text><path d="M10870.5 4062.67C10870.5 4006.24 10916.2 3960.5 10972.7 3960.5L11768.3 3960.5C11824.8 3960.5 11870.5 4006.24 11870.5 4062.67L11870.5 4471.33C11870.5 4527.76 11824.8 4573.5 11768.3 4573.5L10972.7 4573.5C10916.2 4573.5 10870.5 4527.76 10870.5 4471.33Z" stroke="#000000" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 11004.5 4207)">NVMe</text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 11645.6 4207)">/</text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 11078.1 4471)">Block</text><path d="M10645 5063.5C10664.1 5063.5 10679.5 5078.95 10679.5 5098 10679.5 5117.05 10664.1 5132.5 10645 5132.5 10625.9 5132.5 10610.5 5117.05 10610.5 5098 10610.5 5078.95 10625.9 5063.5 10645 5063.5Z" stroke="#000000" stroke-width="3.4375" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><path d="M10477 5063.5C10496.1 5063.5 10511.5 5078.95 10511.5 5098 10511.5 5117.05 10496.1 5132.5 10477 5132.5 10457.9 5132.5 10442.5 5117.05 10442.5 5098 10442.5 5078.95 10457.9 5063.5 10477 5063.5Z" stroke="#000000" stroke-width="3.4375" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><path d="M10309 5063.5C10328.1 5063.5 10343.5 5078.95 10343.5 5098 10343.5 5117.05 10328.1 5132.5 10309 5132.5 10289.9 5132.5 10274.5 5117.05 10274.5 5098 10274.5 5078.95 10289.9 5063.5 10309 5063.5Z" stroke="#000000" stroke-width="3.4375" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><g clip-path="url(#clip13)" filter="url(#fx3)" transform="translate(8167 2650)"><g><g><path d="M12.0943-10.4751 763.082 856.593 738.894 877.544-12.0943 10.4751ZM800.984 802.599 845.264 975.917 680.042 907.35Z" fill="#016EC0" fill-rule="nonzero" fill-opacity="1" transform="matrix(1 0 0 -1 28 1153.14)"/></g></g></g><path d="M12.1258-10.5024 762.868 856.283 738.617 877.288-12.1258 10.5024ZM800.869 802.147 845.264 975.917 679.611 907.172Z" fill="#016EC0" fill-rule="nonzero" fill-opacity="1" transform="matrix(1 0 0 -1 8195 3795.92)"/><g clip-path="url(#clip14)" filter="url(#fx4)" transform="translate(8174 3772)"><g><g><path d="M26.4426 16.1719 736.295 272.949 725.409 303.041 15.5574 46.2636ZM743.019 207.323 866.264 336.978 688.593 357.781Z" fill="#016EC0" fill-rule="nonzero" fill-opacity="1"/></g></g></g><path d="M8200.46 3780.92 8909.96 4037.56 8899.04 4067.73 8189.54 3811.08ZM8916.7 3971.77 9040.26 4101.76 8862.13 4122.62Z" fill="#016EC0" fill-rule="nonzero" fill-opacity="1"/><path d="M1186.5 2619.5C1186.5 2528.37 1260.37 2454.5 1351.5 2454.5L2506.5 2454.5C2597.63 2454.5 2671.5 2528.37 2671.5 2619.5L2671.5 3279.5C2671.5 3370.63 2597.63 3444.5 2506.5 3444.5L1351.5 3444.5C1260.37 3444.5 1186.5 3370.63 1186.5 3279.5Z" stroke="#003526" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1484.62 2758)">Backend </text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1337.68 3022)">Connection </text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1731.53 3286)">info</text><path d="M1299.5 2731.5C1299.5 2640.37 1373.37 2566.5 1464.5 2566.5L2619.5 2566.5C2710.63 2566.5 2784.5 2640.37 2784.5 2731.5L2784.5 3391.5C2784.5 3482.63 2710.63 3556.5 2619.5 3556.5L1464.5 3556.5C1373.37 3556.5 1299.5 3482.63 1299.5 3391.5Z" stroke="#003526" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1597.18 2870)">Backend </text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1450.24 3134)">Connection </text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1844.08 3398)">info</text><path d="M1412.5 2843.5C1412.5 2752.37 1486.37 2678.5 1577.5 2678.5L2732.5 2678.5C2823.63 2678.5 2897.5 2752.37 2897.5 2843.5L2897.5 3503.5C2897.5 3594.63 2823.63 3668.5 2732.5 3668.5L1577.5 3668.5C1486.37 3668.5 1412.5 3594.63 1412.5 3503.5Z" stroke="#003526" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1701.92 2982)">Backend </text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1550.76 3246)">Connection </text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1941.77 3510)">Info</text><path d="M1186.5 4074.5C1186.5 3965.15 1275.15 3876.5 1384.5 3876.5L2473.5 3876.5C2582.85 3876.5 2671.5 3965.15 2671.5 4074.5L2671.5 4866.5C2671.5 4975.85 2582.85 5064.5 2473.5 5064.5L1384.5 5064.5C1275.15 5064.5 1186.5 4975.85 1186.5 4866.5Z" stroke="#003526" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1484.62 4279)">Backend </text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1337.68 4543)">Connection </text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1731.53 4807)">info</text><path d="M1299.5 4186.5C1299.5 4077.15 1388.15 3988.5 1497.5 3988.5L2586.5 3988.5C2695.85 3988.5 2784.5 4077.15 2784.5 4186.5L2784.5 4978.5C2784.5 5087.85 2695.85 5176.5 2586.5 5176.5L1497.5 5176.5C1388.15 5176.5 1299.5 5087.85 1299.5 4978.5Z" stroke="#003526" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1597.18 4391)">Backend </text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1450.24 4655)">Connection </text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1844.08 4919)">info</text><path d="M1412.5 4298.5C1412.5 4189.15 1501.15 4100.5 1610.5 4100.5L2699.5 4100.5C2808.85 4100.5 2897.5 4189.15 2897.5 4298.5L2897.5 5090.5C2897.5 5199.85 2808.85 5288.5 2699.5 5288.5L1610.5 5288.5C1501.15 5288.5 1412.5 5199.85 1412.5 5090.5Z" stroke="#003526" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1721.49 4371)">Memory </text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1755.57 4635)">Section </text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1744.98 4899)">Remote </text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 1602.99 5163)">Identifiers</text><g clip-path="url(#clip15)" filter="url(#fx5)" transform="translate(2720 3003)"><g><g><path d="M1178.2 820.434 290.721 266.992 307.654 239.839 1195.13 793.281ZM270.432 329.765 177 177.218 355.097 194Z" fill="#016EC0" fill-rule="nonzero" fill-opacity="1"/></g></g></g><path d="M3898.17 3816.25 3011.02 3263.01 3027.99 3235.78 3915.15 3789.03ZM2990.68 3325.94 2897 3173 3075.56 3189.83Z" fill="#016EC0" fill-rule="nonzero" fill-opacity="1"/><g clip-path="url(#clip16)" filter="url(#fx6)" transform="translate(2720 3782)"><g><g><path d="M10.5867-11.9967 912.279 783.721 891.105 807.714-10.5867 11.9967ZM942.629 725.147 1009.66 890.998 836.762 845.114Z" fill="#016EC0" fill-rule="nonzero" fill-opacity="1" transform="matrix(-1 0 0 1 1186.66 28.2178)"/></g></g></g><path d="M10.6143-12.0279 912.026 783.442 890.797 807.498-10.6143 12.0279ZM942.455 724.715 1009.66 890.998 836.311 844.995Z" fill="#016EC0" fill-rule="nonzero" fill-opacity="1" transform="matrix(-1 0 0 1 3906.66 3803)"/><text fill="#000000" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 6562.6 1284)">NIXL Transfer </text><text fill="#000000" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 6599.06 1581)">Handles with </text><text fill="#000000" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="248" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 6389.67 1878)">repost capability</text><g clip-path="url(#clip17)" filter="url(#fx7)" transform="translate(7105 1758)"><g><g><path d="M23-4.89533e-05 23.0008 370.681-22.9992 370.681-23 4.89533e-05ZM0.000788958 370.681 115.001 278.68 0.00108268 508.681-114.999 278.681Z" fill="#860D56" fill-rule="nonzero" fill-opacity="1" transform="matrix(1 0 0 -1 247 754.898)"/></g></g></g><path d="M22.9167-4.87759e-05 22.9175 371.181-22.9159 371.181-22.9167 4.87759e-05ZM0.000790022 371.181 114.584 279.514 0.00108268 508.681-114.583 279.514Z" fill="#860D56" fill-rule="nonzero" fill-opacity="1" transform="matrix(1 0 0 -1 7352 2505.68)"/><path d="M4261 5380.5C4324.3 5380.5 4375.61 5434.13 4375.61 5500.3 4375.61 5525.11 4368.39 5548.16 4356.04 5567.28L4346.3 5579.61 4665.5 5579.61 4665.5 6073.5 3856.5 6073.5 3856.5 5939.67 3863.8 5937.3C3904.93 5919.11 3933.79 5876.54 3933.79 5826.92 3933.79 5777.29 3904.93 5734.72 3863.8 5716.53L3856.5 5714.17 3856.5 5579.61 4175.7 5579.61 4165.96 5567.28C4153.61 5548.16 4146.39 5525.11 4146.39 5500.3 4146.39 5434.13 4197.7 5380.5 4261 5380.5Z" stroke="#000000" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#018262" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 4059 5906)">UCX</text><path d="M5166.5 5380.5C5226.43 5380.5 5275.02 5434.13 5275.02 5500.3 5275.02 5525.11 5268.18 5548.16 5256.48 5567.28L5247.27 5579.61 5549.5 5579.61 5549.5 6073.5 4783.5 6073.5 4783.5 5939.67 4790.41 5937.3C4829.36 5919.11 4856.69 5876.54 4856.69 5826.92 4856.69 5777.29 4829.36 5734.72 4790.41 5716.53L4783.5 5714.17 4783.5 5579.61 5085.73 5579.61 5076.52 5567.28C5064.82 5548.16 5057.98 5525.11 5057.98 5500.3 5057.98 5434.13 5106.57 5380.5 5166.5 5380.5Z" stroke="#000000" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#5B167F" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 4936.66 5906)">GDS</text><path d="M6050 5380.5C6109.85 5380.5 6158.38 5434.21 6158.38 5500.47 6158.38 5525.32 6151.55 5548.4 6139.87 5567.55L6130.66 5579.9 6432.5 5579.9 6432.5 6074.5 5667.5 6074.5 5667.5 5940.47 5674.4 5938.1C5713.3 5919.89 5740.59 5877.25 5740.59 5827.56 5740.59 5777.87 5713.3 5735.23 5674.4 5717.02L5667.5 5714.65 5667.5 5579.9 5969.34 5579.9 5960.13 5567.55C5948.45 5548.4 5941.62 5525.32 5941.62 5500.47 5941.62 5434.21 5990.15 5380.5 6050 5380.5Z" stroke="#000000" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#00B0F0" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="193" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 5931.8 5883)">S3</text><path d="M7798 5380.5C7857.85 5380.5 7906.38 5434.13 7906.38 5500.3 7906.38 5525.11 7899.55 5548.16 7887.87 5567.28L7878.66 5579.61 8180.5 5579.61 8180.5 6073.5 7415.5 6073.5 7415.5 5939.67 7422.4 5937.3C7461.3 5919.11 7488.59 5876.54 7488.59 5826.92 7488.59 5777.29 7461.3 5734.72 7422.4 5716.53L7415.5 5714.17 7415.5 5579.61 7717.34 5579.61 7708.13 5567.28C7696.45 5548.16 7689.62 5525.11 7689.62 5500.3 7689.62 5434.13 7738.15 5380.5 7798 5380.5Z" stroke="#000000" stroke-width="9.16667" stroke-linecap="butt" stroke-linejoin="round" stroke-miterlimit="10" stroke-opacity="1" fill="#898989" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="147" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 7559.42 5767)">Custom</text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="147" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 7519.06 5925)">backend</text><path d="M4326 4692C4326 4600.87 4399.87 4527 4491 4527L7632 4527C7723.13 4527 7797 4600.87 7797 4692L7797 4692C7797 4783.13 7723.13 4857 7632 4857L4491 4857C4399.87 4857 4326 4783.13 4326 4692Z" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 4663.42 4753)">South</text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 5308.36 4753)">-</text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 5405.19 4753)">Bound Backend API</text><path d="M4320 2671C4320 2579.87 4393.87 2506 4485 2506L7626 2506C7717.13 2506 7791 2579.87 7791 2671L7791 2671C7791 2762.13 7717.13 2836 7626 2836L4485 2836C4393.87 2836 4320 2762.13 4320 2671Z" fill="#F3BD01" fill-rule="evenodd" fill-opacity="1"/><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 4859.12 2731)">North</text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 5497 2731)">-</text><text fill="#FFFFFF" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="700" font-stretch="normal" font-size="220" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 5593.82 2731)">Bound NIXL API</text><g clip-path="url(#clip18)" filter="url(#fx8)" transform="translate(5247 4837)"><g><g><path d="M270 16.2178 270 287.704 224 287.704 224 16.2178ZM247 287.704 362 195.703 247 425.704 132 195.704Z" fill="#860D56" fill-rule="nonzero" fill-opacity="1"/></g></g></g><path d="M5516.92 4846 5516.92 5117.99 5471.08 5117.99 5471.08 4846ZM5494 5117.99 5608.58 5026.32 5494 5255.49 5379.42 5026.32Z" fill="#860D56" fill-rule="nonzero" fill-opacity="1"/><g clip-path="url(#clip19)" filter="url(#fx9)" transform="translate(6365 4618)"><g><g><path d="M23-2.08462e-05 23.0002 260.178-22.9998 260.178-23 2.08462e-05ZM0.000235815 260.178 115 168.178 0.000360892 398.178-115 168.178Z" fill="#860D56" fill-rule="nonzero" fill-opacity="1" transform="matrix(1 0 0 -1 247 644.396)"/></g></g></g><path d="M22.9167-2.07707e-05 22.9169 260.678-22.9164 260.678-22.9167 2.07707e-05ZM0.000236268 260.678 114.583 169.012 0.000360892 398.178-114.583 169.012Z" fill="#860D56" fill-rule="nonzero" fill-opacity="1" transform="matrix(1 0 0 -1 6612 5255.18)"/><text fill="#000000" fill-opacity="1" font-family="NVIDIA Sans,NVIDIA Sans_MSFontService,sans-serif" font-style="normal" font-variant="normal" font-weight="400" font-stretch="normal" font-size="761" text-anchor="start" direction="ltr" writing-mode="lr-tb" unicode-bidi="normal" text-decoration="none" transform="matrix(1 0 0 1 6665.69 5867)">…</text></g></g></svg>