name: Run Pre-Commit Hooks

on: [push, pull_request]

jobs:
  pre-commit:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 2
    - name: Get modified files
      id: modified-files
      run: echo "modified_files=$(git diff --name-only -r HEAD^1 HEAD | xargs)" >> $GITHUB_OUTPUT
    - uses: actions/setup-python@v3
    - uses: pre-commit/action@v3.0.0
      with:
        extra_args: --files ${{ steps.modified-files.outputs.modified_files }}