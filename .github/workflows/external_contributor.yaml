# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: External Contributor
on:
  pull_request_target:
    types: [opened]
jobs:
  pr_reminder:
    if: ${{ github.event.pull_request.head.repo.full_name != github.event.pull_request.base.repo.full_name }}
    permissions:
      actions: read
      issues: write
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - name: Reminder to run full CI on PR
        uses: actions/github-script@v7
        env:
          REPOSITORY: ${{ github.repository }}
          CONTRIBUTOR: ${{ github.actor }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          script: |
            const { REPOSITORY, CONTRIBUTOR } = process.env

            github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: `👋 Hi ${CONTRIBUTOR}! Thank you for contributing to ${REPOSITORY}.\n\n` +
                'Your PR reviewers will review your contribution then trigger the CI to test your changes.\n\n' +
                '🚀'
            })
      - name: Adding Label to PR
        uses: actions/github-script@v7
        env:
          REPOSITORY: ${{ github.repository }}
          CONTRIBUTOR: ${{ github.actor }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          script: |
            const { REPOSITORY, CONTRIBUTOR } = process.env

            github.rest.issues.addLabels({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              labels: ["external-contribution"]
            });
