name: AWS EFA NIXL Validation

on:
  push:
    branches:
    - main
    - "pull-request/[0-9]+"

jobs:
  run_aws_tests:
    name: Run AWS Tests - ${{ matrix.test_name }}
    environment: SWX_AWS
    runs-on: ubuntu-latest
    env:
      AWS_DEFAULT_REGION: eu-central-1
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - test_name: "C++ Tests"
            test_scripts:
              - .gitlab/test_cpp.sh $NIXL_INSTALL_DIR
          - test_name: "Python Tests"
            test_scripts:
              - .gitlab/test_python.sh $NIXL_INSTALL_DIR

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Setup
        run: |
          set -exE
          sudo apt-get -qq update
          sudo apt-get -qq install -y curl gettext git gzip jq tar unzip

          # Install kubectl
          curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
          sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

          # Install AWS CLI
          curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
          unzip -qq awscliv2.zip
          sudo ./aws/install --update

          # Verify AWS credentials
          aws sts get-caller-identity >/dev/null

      - name: Run AWS tests
        working-directory: ./contrib/aws-efa
        timeout-minutes: 180
        run: |
          set -o pipefail
          test_cmd='${{ join(matrix.test_scripts, ' && ') }}'
          ./aws_test.sh "$test_cmd" 2>&1 | tee test_output.log

      - name: Cleanup AWS resources
        if: always()
        working-directory: ./contrib/aws-efa
        run: |
          JOB_ID=$(grep 'JOB_ID=' test_output.log | head -n1 | cut -d= -f2)
          echo "JOB_ID=$JOB_ID"
          aws batch terminate-job \
            --job-id "$JOB_ID" \
            --reason 'Terminated by GitHub Actions' || true
