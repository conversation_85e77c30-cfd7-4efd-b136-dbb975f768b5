# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

[build-system]
requires = ["meson-python", "pybind11", "patchelf", "pyyaml", "types-PyYAML", "pytest"]
build-backend = "mesonpy"

[project]
name = 'nixl'
version = '0.3.1'
description = 'NIXL Python API'
readme = 'README.md'
license = {file = 'LICENSE'}
requires-python = '>=3.9'
authors = [
  {name = 'NIXL Developers', email = '<EMAIL>'}
]

dependencies = ["torch", "numpy"]

[tool.isort]
profile = "black"

[tool.meson-python.args]
setup = ['-Dinstall_headers=false']
