# SPDX-FileCopyrightText: Copyright (c) 2024-2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
repos:
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.14.1
    hooks:
      - id: mypy
        files: \.py$

  - repo: https://github.com/timothycrosley/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black", "--filter-files"]

  - repo: https://github.com/psf/black
    rev: 23.1.0
    hooks:
      - id: black
        types_or: [python, cython]

  - repo: https://github.com/PyCQA/flake8
    rev: 7.1.2
    hooks:
      - id: flake8
        args: ["--max-line-length=88", "--select=C,E,F,W,B,B950", "--extend-ignore=E203,E501,W503"]
        types_or: [python, cython]

# Removed to allow clang format to proceed and PRs to pass pre-commit and CI.
# Once Clang format checker is enabled in CI, conformance will be mandatory.
# See: https://clang.llvm.org/docs/ClangFormat.html#vim-integration for integrating clang-formatting.
#  - repo: https://github.com/pre-commit/mirrors-clang-format
#    rev: v16.0.5
#    hooks:
#      - id: clang-format
#        types_or: [c, c++, cuda, proto, textproto, java]
#        args: ["-fallback-style=none", "-style=file", "-i"]
#        files: \.(cpp|h|cc|c)$

  - repo: https://github.com/codespell-project/codespell
    rev: v2.2.4
    hooks:
      - id: codespell
        args: ["--toml", "pyproject.toml"]
        exclude: (?x)^(.*stemmer.*|.*stop_words.*|^CHANGELOG.md$|.*\.md|.*\.rs$|src/utils/serdes/serdes.h)

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: check-case-conflict
      - id: check-executables-have-shebangs
      - id: check-merge-conflict
      - id: check-json
      - id: check-toml
      - id: check-yaml
      - id: check-shebang-scripts-are-executable
      - id: end-of-file-fixer
        types_or: [c, c++, cuda, proto, textproto, java, python]
      - id: mixed-line-ending
      - id: requirements-txt-fixer
      - id: trailing-whitespace
